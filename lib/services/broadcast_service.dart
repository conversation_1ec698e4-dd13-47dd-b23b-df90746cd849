import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'crypto_service.dart';

class BroadcastService {
  RawDatagramSocket? _socket;

  Future<Map<String, String>?> listenForBroadcast({
    Duration timeout = const Duration(seconds: 30),
  }) async {
    final completer = Completer<Map<String, String>?>();

    try {
      _socket = await RawDatagramSocket.bind(InternetAddress.anyIPv4, 9999);
      _socket?.broadcastEnabled = true;
      debugPrint('Listening for server broadcast on port 9999...');

      final timer = Timer(timeout, () {
        if (!completer.isCompleted) {
          debugPrint('Broadcast discovery timed out.');
          completer.complete(null);
          close();
        }
      });

      _socket?.listen((RawSocketEvent event) {
        if (event == RawSocketEvent.read) {
          final datagram = _socket?.receive();
          if (datagram != null) {
            final message = String.fromCharCodes(datagram.data);
            debugPrint('Received broadcast message: $message');

            if (message.contains('SERVER_ADDRESS:') &&
                message.contains('Salt:')) {
              final parts = message.split(';');
              final serverAddressPart = parts.firstWhere(
                (part) => part.startsWith('SERVER_ADDRESS:'),
                orElse: () => '',
              );
              final saltPart = parts.firstWhere(
                (part) => part.startsWith('Salt:'),
                orElse: () => '',
              );
              print("saltPart: $saltPart");
              if (serverAddressPart.isNotEmpty && saltPart.isNotEmpty) {
                final address = serverAddressPart.split(':')[1];
                final encryptedSalt = saltPart.split(':')[1];
                final decryptedSalt = CryptoService.decryptSalt(encryptedSalt);

                if (decryptedSalt != null && !completer.isCompleted) {
                  completer.complete({
                    'serverAddress': address,
                    'salt': decryptedSalt,
                  });
                  timer.cancel();
                  close();
                }
              }
            } else if (message.startsWith('SERVER_ADDRESS=')) {
              final address = message.split('=')[1];
              if (!completer.isCompleted) {
                completer.complete({'serverAddress': address});
                timer.cancel();
                close();
              }
            }
          }
        }
      });
    } catch (e) {
      debugPrint('Error listening for broadcast: $e');
      if (!completer.isCompleted) {
        completer.complete(null);
      }
      close();
    }

    return completer.future;
  }

  void close() {
    _socket?.close();
    _socket = null;
    debugPrint('Broadcast socket closed.');
  }
}
