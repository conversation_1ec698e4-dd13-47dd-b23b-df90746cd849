import 'dart:io';
import 'package:mac_address_plus/mac_address_plus.dart';

class MacAddressService {
  final _macAddressPlusPlugin = MacAddressPlus();

  // Get device ID based on platform
  Future<String> getMacAddress() async {
    try {
      if (Platform.isAndroid) {
        return await _getAndroidMacAddress();
      } else {
        return 'unknown_device';
      }
    } catch (e) {
      print('Error getting Mac Address: $e');
      return 'error_mac_address';
    }
  }

  // Get Android device ID
  Future<String> _getAndroidMacAddress() async {
    final macAddress =
        await _macAddressPlusPlugin.getMacAddress() ?? 'unknown_mac_address';
    return macAddress;
  }
}
