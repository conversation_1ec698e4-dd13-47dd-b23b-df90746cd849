import 'dart:convert';
import 'package:crypto/crypto.dart' as crypto;
import 'package:encrypt/encrypt.dart' as enc;
import 'package:flutter/foundation.dart';

class CryptoService {
  static const String _keyStr = 'fsMufong';

  static String? decryptSalt(String encryptedSalt) {
    try {
      // 密钥是 _keyStr 的 SHA-256 哈希值
      final keyHash = crypto.sha256.convert(utf8.encode(_keyStr));
      final key = enc.Key(Uint8List.fromList(keyHash.bytes));

      // 加密的文本是 Base64 编码的，它包含 IV + 密文。
      final encryptedDataWithIv = base64.decode(encryptedSalt);

      // 提取 IV (AES 的前 16 个字节)
      final iv = enc.IV(encryptedDataWithIv.sublist(0, 16));

      // 剩下的是加密数据
      final encrypted = enc.Encrypted(encryptedDataWithIv.sublist(16));

      // 创建解密器
      final encrypter = enc.Encrypter(
        enc.AES(key, mode: enc.AESMode.cbc, padding: 'PKCS7'),
      );

      // 解密
      final decrypted = encrypter.decrypt(encrypted, iv: iv);

      return decrypted;
    } catch (e) {
      debugPrint('Error decrypting salt: $e');
      return null;
    }
  }

  static String generateRegistrationCode(String macAddress, String salt) {
    // 1. 标准化 MAC 地址 (移除分隔符并转为小写)
    final standardizedMac = macAddress.replaceAll(RegExp(r'[:\-]'), '');

    // 2. 计算 HMAC-SHA256
    final key = utf8.encode(salt);
    final message = utf8.encode(standardizedMac);
    final hmacSha256 = crypto.Hmac(crypto.sha256, key);
    final digest = hmacSha256.convert(message);

    // 3. 格式化注册码
    final hexDigest = digest.toString();
    final shortHex = hexDigest.substring(4, 12).toUpperCase();

    return shortHex;
  }
}
