import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/settings_model.dart';

class SettingsService {
  static const String _settingsKey = 'esop_settings';

  // Save settings to shared preferences
  Future<bool> saveSettings(SettingsModel settings) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String settingsJson = jsonEncode(settings.toJson());
      return await prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      debugPrint('Error saving settings: $e');
      return false;
    }
  }

  // Load settings from shared preferences
  Future<SettingsModel?> loadSettings() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String? settingsJson = prefs.getString(_settingsKey);

      if (settingsJson == null) {
        return null;
      }

      return SettingsModel.fromJson(jsonDecode(settingsJson));
    } catch (e) {
      debugPrint('Error loading settings: $e');
      return null;
    }
  }

  // Update MQTT server address
  Future<bool> updateMqttServerAddress(String address) async {
    try {
      final settings = await loadSettings() ?? SettingsModel();
      settings.mqttServerAddress = address;
      return await saveSettings(settings);
    } catch (e) {
      debugPrint('Error updating MQTT server address: $e');
      return false;
    }
  }

  // Update device alias
  Future<bool> updateDeviceAlias(String alias) async {
    try {
      final settings = await loadSettings() ?? SettingsModel();
      settings.deviceAlias = alias;
      return await saveSettings(settings);
    } catch (e) {
      debugPrint('Error updating device alias: $e');
      return false;
    }
  }

  // Update device ID
  Future<bool> updateDeviceId(String deviceId) async {
    try {
      final settings = await loadSettings() ?? SettingsModel();
      settings.deviceId = deviceId;
      return await saveSettings(settings);
    } catch (e) {
      debugPrint('Error updating device ID: $e');
      return false;
    }
  }

  // Update language code
  Future<bool> updateLanguageCode(String languageCode) async {
    try {
      final settings = await loadSettings() ?? SettingsModel();
      settings.languageCode = languageCode;
      return await saveSettings(settings);
    } catch (e) {
      debugPrint('Error updating language code: $e');
      return false;
    }
  }

  // Update MQTT port
  Future<bool> updateMqttPort(String port) async {
    try {
      final settings = await loadSettings() ?? SettingsModel();
      settings.mqttPort = port;
      return await saveSettings(settings);
    } catch (e) {
      debugPrint('Error updating MQTT port: $e');
      return false;
    }
  }

  // Update Server port
  Future<bool> updateServerPort(String port) async {
    try {
      final settings = await loadSettings() ?? SettingsModel();
      settings.serverPort = port;
      return await saveSettings(settings);
    } catch (e) {
      debugPrint('Error updating Server port: $e');
      return false;
    }
  }

  // Update MQTT topic
  Future<bool> updateMqttTopic(String topic) async {
    try {
      final settings = await loadSettings() ?? SettingsModel();
      settings.mqttTopic = topic;
      return await saveSettings(settings);
    } catch (e) {
      debugPrint('Error updating MQTT topic: $e');
      return false;
    }
  }

  // Update group name
  Future<bool> updateGroupName(String groupName) async {
    try {
      final settings = await loadSettings() ?? SettingsModel();
      settings.groupName = groupName;
      return await saveSettings(settings);
    } catch (e) {
      debugPrint('Error updating group name: $e');
      return false;
    }
  }

  // Update screen orientation
  Future<bool> updateScreenOrientation(String orientation) async {
    try {
      final settings = await loadSettings() ?? SettingsModel();
      settings.screenOrientation = orientation;
      return await saveSettings(settings);
    } catch (e) {
      debugPrint('Error updating screen orientation: $e');
      return false;
    }
  }

  // Update registration code
  Future<bool> updateRegistrationCode(String registrationCode) async {
    try {
      final settings = await loadSettings() ?? SettingsModel();
      settings.registrationCode = registrationCode;
      return await saveSettings(settings);
    } catch (e) {
      debugPrint('Error updating registration code: $e');
      return false;
    }
  }
}
