import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/mqtt_message_model.dart';
import '../services/mqtt_service.dart';

class MqttProvider with ChangeNotifier {
  final MqttService _mqttService = MqttService();
  
  MqttConnectionState _connectionState = MqttConnectionState.disconnected;
  String _error = '';
  StreamSubscription? _connectionSubscription;
  StreamSubscription? _messageSubscription;
  
  // Getters
  MqttConnectionState get connectionState => _connectionState;
  String get error => _error;
  bool get isConnected => _connectionState == MqttConnectionState.connected;
  
  // Initialize MQTT service
  void initialize() {
    // Listen for connection state changes
    _connectionSubscription = _mqttService.connectionState.listen((state) {
      _connectionState = state;
      notifyListeners();
    });
    
    // Connect to MQTT server
    connect();
  }
  
  // Connect to MQTT server
  Future<void> connect() async {
    try {
      final success = await _mqttService.connect();
      
      if (!success) {
        _error = 'Failed to connect to MQTT server';
      } else {
        _error = '';
      }
    } catch (e) {
      _error = 'Error connecting to MQTT server: $e';
      print(_error);
    }
    
    notifyListeners();
  }
  
  // Disconnect from MQTT server
  Future<void> disconnect() async {
    try {
      await _mqttService.disconnect();
      _error = '';
    } catch (e) {
      _error = 'Error disconnecting from MQTT server: $e';
      print(_error);
    }
    
    notifyListeners();
  }
  
  // Listen for MQTT messages
  void listenForMessages(Function(MqttMessageModel) onMessage) {
    // Cancel previous subscription if exists
    _messageSubscription?.cancel();
    
    // Listen for messages
    _messageSubscription = _mqttService.messages.listen((message) {
      onMessage(message);
    });
  }
  
  // Dispose resources
  @override
  void dispose() {
    _connectionSubscription?.cancel();
    _messageSubscription?.cancel();
    _mqttService.dispose();
    super.dispose();
  }
}
