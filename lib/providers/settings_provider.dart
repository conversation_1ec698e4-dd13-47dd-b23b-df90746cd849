import 'package:flutter/foundation.dart';
import '../models/settings_model.dart';
import '../services/settings_service.dart';
import '../services/device_info_service.dart';
import '../services/mac_address_service.dart';
import '../services/crypto_service.dart';

class SettingsProvider with ChangeNotifier {
  final SettingsService _settingsService = SettingsService();
  final DeviceInfoService _deviceInfoService = DeviceInfoService();
  final MacAddressService _macAddressService = MacAddressService();
  SettingsModel _settings = SettingsModel();
  bool _isLoading = false;
  String _error = '';

  // Getters
  SettingsModel get settings => _settings;
  bool get isLoading => _isLoading;
  String get error => _error;

  // Initialize settings
  Future<void> initSettings() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Load settings from storage
      final savedSettings = await _settingsService.loadSettings();

      if (savedSettings != null) {
        _settings = savedSettings;
      }

      // Get device ID if not already set
      if (_settings.macAddress == null || _settings.macAddress!.isEmpty) {
        final macAddress = await _macAddressService.getMacAddress();
        _settings.macAddress = macAddress;
        await _settingsService.saveSettings(_settings);
      }

      _error = '';
    } catch (e) {
      _error = 'Error initializing settings: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update MQTT server address
  Future<void> updateMqttServerAddress(String address) async {
    _isLoading = true;
    notifyListeners();

    try {
      final success = await _settingsService.updateMqttServerAddress(address);

      if (success) {
        _settings.mqttServerAddress = address;
        _error = '';
      } else {
        _error = 'Failed to update MQTT server address';
      }
    } catch (e) {
      _error = 'Error updating MQTT server address: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update device alias
  Future<void> updateDeviceAlias(String alias) async {
    _isLoading = true;
    notifyListeners();

    try {
      final success = await _settingsService.updateDeviceAlias(alias);

      if (success) {
        _settings.deviceAlias = alias;
        _error = '';
      } else {
        _error = 'Failed to update device alias';
      }
    } catch (e) {
      _error = 'Error updating device alias: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update language code
  Future<void> updateLanguageCode(String languageCode) async {
    _isLoading = true;
    notifyListeners();

    try {
      final success = await _settingsService.updateLanguageCode(languageCode);

      if (success) {
        _settings.languageCode = languageCode;
        _error = '';
      } else {
        _error = 'Failed to update language';
      }
    } catch (e) {
      _error = 'Error updating language: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update MQTT port
  Future<void> updateMqttPort(String port) async {
    _isLoading = true;
    notifyListeners();

    try {
      final success = await _settingsService.updateMqttPort(port);

      if (success) {
        _settings.mqttPort = port;
        _error = '';
      } else {
        _error = 'Failed to update MQTT port';
      }
    } catch (e) {
      _error = 'Error updating MQTT port: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update Server port
  Future<void> updateServerPort(String port) async {
    _isLoading = true;
    notifyListeners();

    try {
      final success = await _settingsService.updateServerPort(port);

      if (success) {
        _settings.serverPort = port;
        _error = '';
      } else {
        _error = 'Failed to update Server port';
      }
    } catch (e) {
      _error = 'Error updating Server port: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update MQTT topic
  Future<void> updateMqttTopic(String topic) async {
    _isLoading = true;
    notifyListeners();

    try {
      final success = await _settingsService.updateMqttTopic(topic);

      if (success) {
        _settings.mqttTopic = topic;
        _error = '';
      } else {
        _error = 'Failed to update MQTT topic';
      }
    } catch (e) {
      _error = 'Error updating MQTT topic: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update group name
  Future<void> updateGroupName(String groupName) async {
    _isLoading = true;
    notifyListeners();

    try {
      final success = await _settingsService.updateGroupName(groupName);

      if (success) {
        _settings.groupName = groupName;
        _error = '';
      } else {
        _error = 'Failed to update group name';
      }
    } catch (e) {
      _error = 'Error updating group name: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update screen orientation
  Future<void> updateScreenOrientation(String orientation) async {
    _isLoading = true;
    notifyListeners();

    try {
      final success = await _settingsService.updateScreenOrientation(
        orientation,
      );

      if (success) {
        _settings.screenOrientation = orientation;
        _error = '';
      } else {
        _error = 'Failed to update screen orientation';
      }
    } catch (e) {
      _error = 'Error updating screen orientation: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update registration code
  Future<void> updateRegistrationCode(String registrationCode) async {
    _isLoading = true;
    notifyListeners();

    try {
      final success = await _settingsService.updateRegistrationCode(
        registrationCode,
      );

      if (success) {
        _settings.registrationCode = registrationCode;
        _error = '';
      } else {
        _error = 'Failed to update registration code';
      }
    } catch (e) {
      _error = 'Error updating registration code: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> generateAndSaveRegistrationCode(String salt) async {
    _isLoading = true;
    notifyListeners();

    try {
      final macAddress = await _macAddressService.getMacAddress();
      final registrationCode = CryptoService.generateRegistrationCode(
        macAddress,
        salt,
      );
      await updateRegistrationCode(registrationCode);
      _error = '';
    } catch (e) {
      _error = 'Error generating registration code: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
