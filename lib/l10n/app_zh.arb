{"appTitle": "ESOP 客户端", "settings": "设置", "language": "语言", "english": "英文", "chinese": "中文", "settingsSaved": "设置已保存", "mqttServerAddress": "服务器地址", "mqttServerAddressHint": "例如：example.com 或 ***********", "pleaseEnterMqttServerAddress": "请输入 服务器地址", "apiServerPort": "Api 端口", "apiServerPortHint": "例如：8090", "mqttPort": "MQTT 端口", "mqttPortHint": "例如：1883", "pleaseEnterMqttPort": "请输入 MQTT 端口", "portNumberMustBeBetween": "端口号必须在 1-65535 之间", "pleaseEnterValidPort": "请输入有效的端口号", "mqttTopic": "MQTT 主题", "mqttTopicHint": "例如：esopChannel", "pleaseEnterMqttTopic": "请输入 MQTT 主题", "groupName": "生产线", "groupNameHint": "例如：生产线 1", "pleaseEnterGroupName": "请输入分组名称", "deviceAlias": "设备别名", "deviceAliasHint": "例如：我的设备", "pleaseEnterDeviceAlias": "请输入设备别名", "registrationCode": "注册码", "registrationCodeHint": "例如：ABC123", "pleaseEnterRegistrationCode": "请输入注册码", "deviceId": "设备 ID", "macAddress": " MAC 地址", "loading": "加载中...", "saveSettings": "保存设置", "connected": "已连接", "disconnected": "未连接", "mqttStatus": "MQTT 状态: {status}", "@mqttStatus": {"placeholders": {"status": {"type": "String"}}}, "connect": "连接", "doubleTapWithTwoFingers": "双击进入设置", "fileOperation": "文件操作: {status}", "@fileOperation": {"placeholders": {"status": {"type": "String"}}}, "retry": "重试", "usingExistingFile": "使用已存在的文件", "checking": "检查中", "openingDocument": "正在打开文档...", "errorLoadingContent": "加载内容错误: {message}", "@errorLoadingContent": {"placeholders": {"message": {"type": "String"}}}, "httpError": "HTTP 错误: {statusCode} {description}", "@httpError": {"placeholders": {"statusCode": {"type": "int"}, "description": {"type": "String"}}}, "error": "错误", "back": "返回", "screenOrientation": "屏幕方向", "landscapeDisplay": "横屏显示", "portraitDisplay": "竖屏显示", "discovering": "正在发现...", "discoveryFailed": "服务器发现失败，请手动输入地址。", "registrationFailed": "注册失败"}