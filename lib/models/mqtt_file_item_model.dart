class MqttFileItemModel {
  String? downloadFile;
  String? equipmentAliasName;
  String? fileType;

  MqttFileItemModel({this.downloadFile, this.equipmentAliasName});

  MqttFileItemModel.fromJson(Map<String, dynamic> json) {
    downloadFile = json['download_file'];
    fileType = json['file_type'];
    equipmentAliasName = json['equipment_alias_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['download_file'] = downloadFile;
    data['file_type'] = fileType;
    data['equipment_alias_name'] = equipmentAliasName;
    return data;
  }
}
