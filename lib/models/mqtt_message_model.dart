import 'mqtt_file_item_model.dart';

class MqttMessageModel {
  int? type; // 1: partial, 2: all, 3: rule-based
  String? groupName; // "*" for all groups, other string for specific group
  List<MqttFileItemModel>? fileList;

  MqttMessageModel({this.type, this.groupName, this.fileList});

  MqttMessageModel.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    groupName = json['group_name'];

    if (json['list'] != null) {
      fileList = <MqttFileItemModel>[];
      json['list'].forEach((v) {
        fileList!.add(MqttFileItemModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    data['group_name'] = groupName;

    if (fileList != null) {
      data['list'] = fileList!.map((v) => v.to<PERSON>son()).toList();
    }
    return data;
  }
}
