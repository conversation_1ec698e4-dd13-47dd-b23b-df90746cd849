import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:microsoft_viewer/microsoft_viewer.dart';

class OfficeViewerScreen extends StatefulWidget {
  final String filePath;

  const OfficeViewerScreen({super.key, required this.filePath});

  @override
  State<OfficeViewerScreen> createState() => _OfficeViewerScreenState();
}

class _OfficeViewerScreenState extends State<OfficeViewerScreen> {
  Uint8List? _fileBytes;

  @override
  void initState() {
    super.initState();
    _loadFile();
  }

  Future<void> _loadFile() async {
    final file = File(widget.filePath);
    if (await file.exists()) {
      final bytes = await file.readAsBytes();
      setState(() {
        _fileBytes = bytes;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final fileExtension = widget.filePath.split('.').last;
    return Scaffold(
      appBar: AppBar(title: Text(widget.filePath.split('/').last)),
      body: _fileBytes != null
          ? MicrosoftViewer(_fileBytes!, false)
          : const Center(child: CircularProgressIndicator()),
    );
  }
}
