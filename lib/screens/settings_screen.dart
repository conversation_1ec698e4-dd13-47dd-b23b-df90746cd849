import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../providers/localization_provider.dart';
import '../l10n/app_localizations_extension.dart';
import '../services/broadcast_service.dart';
import '../services/equipment_api_service.dart';
import '../services/orientation_service.dart';
import '../utils/ui_utils.dart';
import '../widgets/custom_text_form_field.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mqttServerController = TextEditingController();
  final _apiServerController = TextEditingController();
  final _mqttPortController = TextEditingController();
  final _groupNameController = TextEditingController();
  final _deviceAliasController = TextEditingController();
  final _registrationCodeController = TextEditingController();
  final BroadcastService _broadcastService = BroadcastService();
  late final SettingsProvider _settingsProvider;

  String _selectedLanguage = 'zh';
  String _selectedOrientation = 'landscape';
  bool _isDiscovering = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    UiUtils.setFullscreenMode();

    _settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    _loadSettings();

    _settingsProvider.addListener(_onSettingsChanged);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_mqttServerController.text.isEmpty) {
        _discoverServer();
      }
    });
  }

  void _loadSettings() {
    final settings = _settingsProvider.settings;
    _mqttServerController.text = settings.mqttServerAddress ?? '';
    _apiServerController.text = settings.serverPort ?? '8090';
    _mqttPortController.text = settings.mqttPort ?? '1883';
    _groupNameController.text = settings.groupName ?? '';
    _deviceAliasController.text = settings.deviceAlias ?? '';
    _registrationCodeController.text = settings.registrationCode ?? '';

    setState(() {
      _selectedLanguage = settings.languageCode ?? 'zh';
      _selectedOrientation = settings.screenOrientation ?? 'landscape';
    });
  }

  void _onSettingsChanged() {
    if (_isSaving) return;

    final settings = _settingsProvider.settings;
    bool changed = false;

    if (_registrationCodeController.text != (settings.registrationCode ?? '')) {
      _registrationCodeController.text = settings.registrationCode ?? '';
      changed = true;
    }
    if (_groupNameController.text != (settings.groupName ?? '')) {
      _groupNameController.text = settings.groupName ?? '';
      changed = true;
    }
    if (_deviceAliasController.text != (settings.deviceAlias ?? '')) {
      _deviceAliasController.text = settings.deviceAlias ?? '';
      changed = true;
    }

    if (changed && mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _settingsProvider.removeListener(_onSettingsChanged);
    _mqttServerController.dispose();
    _apiServerController.dispose();
    _mqttPortController.dispose();
    _groupNameController.dispose();
    _deviceAliasController.dispose();
    _registrationCodeController.dispose();
    _broadcastService.close();
    super.dispose();
  }

  Future<void> _discoverServer() async {
    if (_isDiscovering) return;

    setState(() {
      _isDiscovering = true;
    });

    final discoveryResult = await _broadcastService.listenForBroadcast(
      timeout: const Duration(seconds: 10),
    );

    if (mounted) {
      setState(() {
        _isDiscovering = false;
      });

      if (discoveryResult != null) {
        final serverAddress = discoveryResult['serverAddress'];
        final salt = discoveryResult['salt'];

        if (serverAddress != null) {
          setState(() {
            _mqttServerController.text = serverAddress;
          });

          if (salt != null) {
            await _settingsProvider.generateAndSaveRegistrationCode(salt);
          }
          _submit(isAutoDiscovered: true);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(context.l10n.discoveryFailed)));
        }
      }
    }
  }

  Future<bool> _saveSettings() async {
    final localizationProvider = Provider.of<LocalizationProvider>(
      context,
      listen: false,
    );

    await _settingsProvider.updateMqttServerAddress(_mqttServerController.text);
    await _settingsProvider.updateServerPort(_apiServerController.text);
    await _settingsProvider.updateMqttPort(_mqttPortController.text);
    await _settingsProvider.updateGroupName(_groupNameController.text);
    await _settingsProvider.updateDeviceAlias(_deviceAliasController.text);
    await _settingsProvider.updateRegistrationCode(
      _registrationCodeController.text.toUpperCase(),
    );

    if (_selectedLanguage != _settingsProvider.settings.languageCode) {
      await _settingsProvider.updateLanguageCode(_selectedLanguage);
      await localizationProvider.changeLocale(Locale(_selectedLanguage));
    }

    if (_selectedOrientation != _settingsProvider.settings.screenOrientation) {
      await _settingsProvider.updateScreenOrientation(_selectedOrientation);
      await OrientationService.applyOrientation(_selectedOrientation);
    }
    return true;
  }

  Future<void> _submit({bool isAutoDiscovered = false}) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    final apiService = EquipmentApiService();
    final settings = _settingsProvider.settings;

    final response = await apiService.addEquipment(
      serverAddress: _mqttServerController.text,
      serverPort: _apiServerController.text,
      deviceAlias: _deviceAliasController.text,
      macAddress: settings.macAddress ?? '',
      groupName: _groupNameController.text,
      aliasName: _deviceAliasController.text,
      registrationCode: _registrationCodeController.text.toUpperCase(),
    );

    if (!mounted) return;

    if (response['code'] == 0) {
      final saved = await _saveSettings();
      if (saved && mounted) {
        if (!isAutoDiscovered) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(context.l10n.settingsSaved)));
        }
        Navigator.of(context).pop();
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '${context.l10n.registrationFailed}: ${response['message']}',
          ),
        ),
      );
    }

    if (mounted) {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(context.l10n.settings)),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(20, 4, 20, 4),
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildLanguageSelection(),
                    const SizedBox(height: 1),
                    _buildOrientationSelection(),
                    const SizedBox(height: 1),
                    CustomTextFormField(
                      controller: _mqttServerController,
                      labelText: context.l10n.mqttServerAddress,
                      hintText: _isDiscovering
                          ? context.l10n.discovering
                          : context.l10n.mqttServerAddressHint,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.l10n.pleaseEnterMqttServerAddress;
                        }
                        return null;
                      },
                      suffixIcon: IconButton(
                        icon: _isDiscovering
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Icon(Icons.search),
                        onPressed: _discoverServer,
                      ),
                    ),
                    CustomTextFormField(
                      controller: _apiServerController,
                      labelText: context.l10n.apiServerPort,
                      hintText: context.l10n.apiServerPortHint,
                      keyboardType: TextInputType.number,
                      validator: _validatePort,
                    ),
                    CustomTextFormField(
                      controller: _mqttPortController,
                      labelText: context.l10n.mqttPort,
                      hintText: context.l10n.mqttPortHint,
                      keyboardType: TextInputType.number,
                      validator: _validatePort,
                    ),
                    CustomTextFormField(
                      controller: _groupNameController,
                      labelText: context.l10n.groupName,
                      hintText: context.l10n.groupNameHint,
                    ),
                    CustomTextFormField(
                      controller: _deviceAliasController,
                      labelText: context.l10n.deviceAlias,
                      hintText: context.l10n.deviceAliasHint,
                    ),
                    CustomTextFormField(
                      controller: _registrationCodeController,
                      labelText: context.l10n.registrationCode,
                      hintText: context.l10n.registrationCodeHint,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.l10n.pleaseEnterRegistrationCode;
                        }
                        return null;
                      },
                    ),
                    TextFormField(
                      initialValue:
                          settingsProvider.settings.macAddress ??
                          context.l10n.loading,
                      decoration: InputDecoration(
                        labelText: context.l10n.macAddress,
                      ),
                      readOnly: true,
                    ),
                    const SizedBox(height: 18),
                    Center(
                      child: ElevatedButton(
                        onPressed: settingsProvider.isLoading ? null : _submit,
                        child: settingsProvider.isLoading
                            ? const CircularProgressIndicator()
                            : Text(context.l10n.saveSettings),
                      ),
                    ),
                    if (settingsProvider.error.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Text(
                          settingsProvider.error,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLanguageSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.language,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        Row(
          children: [
            Expanded(
              child: RadioListTile<String>(
                title: Text(context.l10n.english),
                value: 'en',
                groupValue: _selectedLanguage,
                onChanged: (value) =>
                    setState(() => _selectedLanguage = value!),
              ),
            ),
            Expanded(
              child: RadioListTile<String>(
                title: Text(context.l10n.chinese),
                value: 'zh',
                groupValue: _selectedLanguage,
                onChanged: (value) =>
                    setState(() => _selectedLanguage = value!),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrientationSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.l10n.screenOrientation,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        Row(
          children: [
            Expanded(
              child: RadioListTile<String>(
                title: Text(context.l10n.landscapeDisplay),
                value: 'landscape',
                groupValue: _selectedOrientation,
                onChanged: (value) =>
                    setState(() => _selectedOrientation = value!),
              ),
            ),
            Expanded(
              child: RadioListTile<String>(
                title: Text(context.l10n.portraitDisplay),
                value: 'portrait',
                groupValue: _selectedOrientation,
                onChanged: (value) =>
                    setState(() => _selectedOrientation = value!),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String? _validatePort(String? value) {
    if (value == null || value.isEmpty) {
      return context.l10n.pleaseEnterMqttPort;
    }
    try {
      final port = int.parse(value);
      if (port <= 0 || port > 65535) {
        return context.l10n.portNumberMustBeBetween;
      }
    } catch (e) {
      return context.l10n.pleaseEnterValidPort;
    }
    return null;
  }
}
