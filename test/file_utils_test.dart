import 'package:flutter_test/flutter_test.dart';
import 'package:esop_client/utils/file_utils.dart';

void main() {
  group('FileUtils Tests', () {
    test('should identify document files correctly', () {
      expect(FileUtils.isDocumentFile('test.pdf'), true);
      expect(FileUtils.isDocumentFile('test.doc'), true);
      expect(FileUtils.isDocumentFile('test.docx'), true);
      expect(FileUtils.isDocumentFile('test.xls'), true);
      expect(FileUtils.isDocumentFile('test.xlsx'), true);
      expect(FileUtils.isDocumentFile('test.ppt'), true);
      expect(FileUtils.isDocumentFile('test.pptx'), true);
      expect(FileUtils.isDocumentFile('test.txt'), false);
      expect(FileUtils.isDocumentFile('test.zip'), false);
    });

    test('should identify ZIP files correctly', () {
      expect(FileUtils.isZipFile('test.zip'), true);
      expect(FileUtils.isZipFile('test.pdf'), false);
      expect(FileUtils.isZipFile('test.doc'), false);
    });

    test('should get correct MIME types', () {
      expect(FileUtils.getMimeType('test.pdf'), 'application/pdf');
      expect(FileUtils.getMimeType('test.doc'), 'application/msword');
      expect(
        FileUtils.getMimeType('test.docx'),
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      );
      expect(FileUtils.getMimeType('test.xls'), 'application/vnd.ms-excel');
      expect(
        FileUtils.getMimeType('test.xlsx'),
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      expect(
        FileUtils.getMimeType('test.ppt'),
        'application/vnd.ms-powerpoint',
      );
      expect(
        FileUtils.getMimeType('test.pptx'),
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      );
      expect(FileUtils.getMimeType('test.txt'), null);
    });

    test('should check supported file types correctly', () {
      expect(FileUtils.isSupportedFileType('test.pdf'), true);
      expect(FileUtils.isSupportedFileType('test.zip'), true);
      expect(FileUtils.isSupportedFileType('test.txt'), false);
    });

    test('should handle case insensitive extensions', () {
      expect(FileUtils.isDocumentFile('test.PDF'), true);
      expect(FileUtils.isDocumentFile('test.DOCX'), true);
      expect(FileUtils.isZipFile('test.ZIP'), true);
    });

    test('should get file extensions correctly', () {
      expect(FileUtils.getFileExtension('test.pdf'), '.pdf');
      expect(FileUtils.getFileExtension('test.PDF'), '.pdf');
      expect(FileUtils.getFileExtension('path/to/test.docx'), '.docx');
    });

    test('should get file names correctly', () {
      expect(FileUtils.getFileName('path/to/test.pdf'), 'test.pdf');
      expect(FileUtils.getFileNameWithoutExtension('path/to/test.pdf'), 'test');
    });
  });
}
